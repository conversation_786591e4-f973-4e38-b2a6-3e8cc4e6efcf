// <PERSON><PERSON><PERSON>'s System - Main JavaScript
class VikramSystem {
    constructor() {
        this.gameState = null;
        this.chapters = [];
        this.currentChapter = null;
        this.init();
    }

    async init() {
        await this.loadGameState();
        await this.loadChapters();
        this.updateUI();
        this.setupEventListeners();
        this.showSystemNotification('System Initialized', 'success');
    }

    async loadGameState() {
        try {
            const response = await fetch('/api/gamestate');
            this.gameState = await response.json();
        } catch (error) {
            console.error('Failed to load game state:', error);
            this.showSystemNotification('Failed to load game state', 'error');
        }
    }

    async loadChapters() {
        try {
            const response = await fetch('/api/chapters');
            this.chapters = await response.json();
        } catch (error) {
            console.error('Failed to load chapters:', error);
            this.showSystemNotification('Failed to load chapters', 'error');
        }
    }

    updateUI() {
        if (this.gameState) {
            // Update status bar
            document.getElementById('corruption-points').textContent = this.gameState.gameStats.corruptionPoints || 0;
            document.getElementById('system-points').textContent = this.gameState.gameStats.systemPoints || 0;
            document.getElementById('deviant-exp').textContent = this.gameState.gameStats.deviantExperience || 0;
            document.getElementById('wealth').textContent = this.formatWealth(this.gameState.gameStats.wealth);
            document.getElementById('location').textContent = this.gameState.gameStats.currentLocation || 'Unknown';
            document.getElementById('current-time').textContent = this.gameState.gameStats.currentTime || '--:--';
        }
    }

    formatWealth(amount) {
        if (amount >= 10000000) {
            return Math.floor(amount / 10000000) + 'Cr';
        } else if (amount >= 100000) {
            return Math.floor(amount / 100000) + 'L';
        } else if (amount >= 1000) {
            return Math.floor(amount / 1000) + 'K';
        }
        return amount.toString();
    }

    setupEventListeners() {
        // Close panels when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('panel')) {
                this.hidePanel(e.target.id);
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideAllPanels();
            }
        });
    }

    showSystemNotification(message, type = 'info') {
        const container = document.getElementById('notifications-container');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `[SYSTEM] ${message}`;
        
        container.appendChild(notification);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    hideAllPanels() {
        const panels = document.querySelectorAll('.panel');
        panels.forEach(panel => panel.classList.add('hidden'));
    }

    hidePanel(panelId) {
        document.getElementById(panelId).classList.add('hidden');
    }

    showStory() {
        this.hideAllPanels();
        const panel = document.getElementById('story-panel');
        panel.classList.remove('hidden');
        this.renderChapterList();
    }

    renderChapterList() {
        const container = document.getElementById('chapter-list');
        container.innerHTML = '';

        this.chapters.forEach(chapter => {
            const chapterElement = document.createElement('div');
            chapterElement.className = 'chapter-item';
            chapterElement.innerHTML = `
                <div class="chapter-title">${chapter.title}</div>
                <div class="chapter-meta">
                    Chapter ${chapter.id} • ${chapter.wordCount} words
                </div>
            `;
            chapterElement.onclick = () => this.showChapter(chapter.id);
            container.appendChild(chapterElement);
        });
    }

    async showChapter(chapterId) {
        try {
            const response = await fetch(`/api/chapters/${chapterId}`);
            const chapter = await response.json();
            
            document.getElementById('chapter-list').classList.add('hidden');
            const contentDiv = document.getElementById('chapter-content');
            contentDiv.classList.remove('hidden');
            contentDiv.innerHTML = `
                <button class="btn-secondary" onclick="vikramSystem.backToChapterList()" style="margin-bottom: 20px;">← Back to Chapters</button>
                <div class="chapter-content">${chapter.html}</div>
            `;
            
            this.currentChapter = chapter;
        } catch (error) {
            this.showSystemNotification('Failed to load chapter', 'error');
        }
    }

    backToChapterList() {
        document.getElementById('chapter-content').classList.add('hidden');
        document.getElementById('chapter-list').classList.remove('hidden');
    }

    showCharacters() {
        this.hideAllPanels();
        const panel = document.getElementById('characters-panel');
        panel.classList.remove('hidden');
        this.renderCharacters();
    }

    renderCharacters() {
        const container = document.getElementById('character-list');
        container.innerHTML = '';

        if (!this.gameState.characters || Object.keys(this.gameState.characters).length === 0) {
            container.innerHTML = '<p>No characters found. Characters will appear as you read the story.</p>';
            return;
        }

        Object.entries(this.gameState.characters).forEach(([name, character]) => {
            const characterElement = document.createElement('div');
            characterElement.className = 'character-card';
            characterElement.innerHTML = `
                <div class="character-name">${character.name}</div>
                <div class="character-stats">
                    <div class="stat-item">
                        <span class="stat-label">Designation:</span>
                        <span class="stat-value">${character.designation || 'Unknown'}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Age:</span>
                        <span class="stat-value">${character.age || 'Unknown'}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Status:</span>
                        <span class="stat-value">${character.status || 'Unknown'}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Lust Meter:</span>
                        <span class="stat-value lust-meter">${character.lustMeter || 'Unknown'}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Corruption Potential:</span>
                        <span class="stat-value">${character.corruptionPotential || 'Unknown'}</span>
                    </div>
                </div>
            `;
            container.appendChild(characterElement);
        });
    }

    showQuests() {
        this.hideAllPanels();
        const panel = document.getElementById('quests-panel');
        panel.classList.remove('hidden');
        this.renderQuests();
    }

    renderQuests() {
        const container = document.getElementById('quest-list');
        container.innerHTML = '';

        if (!this.gameState.quests || Object.keys(this.gameState.quests).length === 0) {
            container.innerHTML = '<p>No active quests. Quests will appear as you progress through the story.</p>';
            return;
        }

        Object.entries(this.gameState.quests).forEach(([id, quest]) => {
            const questElement = document.createElement('div');
            questElement.className = 'character-card'; // Reusing character card styling
            questElement.innerHTML = `
                <div class="character-name">${quest.objective}</div>
                <div class="character-stats">
                    <div class="stat-item">
                        <span class="stat-label">Description:</span>
                        <span class="stat-value">${quest.description || 'No description'}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Task:</span>
                        <span class="stat-value">${quest.task || 'No specific task'}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Reward:</span>
                        <span class="stat-value">${quest.reward || 'Unknown'}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Status:</span>
                        <span class="stat-value">${quest.status || 'Active'}</span>
                    </div>
                </div>
            `;
            container.appendChild(questElement);
        });
    }

    showInventory() {
        this.hideAllPanels();
        const panel = document.getElementById('inventory-panel');
        panel.classList.remove('hidden');
        this.renderInventory();
    }

    renderInventory() {
        const container = document.getElementById('inventory-list');
        container.innerHTML = '<p>Inventory system coming soon. Items will be tracked automatically as you acquire them in the story.</p>';
    }

    showShop() {
        this.hideAllPanels();
        const panel = document.getElementById('shop-panel');
        panel.classList.remove('hidden');
        this.renderShop();
    }

    renderShop() {
        const container = document.getElementById('shop-items');
        container.innerHTML = `
            <div class="character-card">
                <div class="character-name">System Shop</div>
                <p>Shop items will be dynamically loaded based on your progress and available CP/SP.</p>
                <p>Current CP: ${this.gameState?.gameStats?.corruptionPoints || 0}</p>
                <p>Current SP: ${this.gameState?.gameStats?.systemPoints || 0}</p>
            </div>
        `;
    }

    openEditor() {
        document.getElementById('editor-modal').classList.remove('hidden');
        // Auto-fill next chapter number
        const nextChapterNum = this.chapters.length > 0 ? Math.max(...this.chapters.map(c => c.id)) + 1 : 4;
        document.getElementById('chapter-number').value = nextChapterNum;
    }

    closeEditor() {
        document.getElementById('editor-modal').classList.add('hidden');
        // Clear form
        document.getElementById('chapter-number').value = '';
        document.getElementById('chapter-title').value = '';
        document.getElementById('chapter-content').value = '';
    }

    async saveChapter() {
        const chapterNumber = document.getElementById('chapter-number').value;
        const chapterTitle = document.getElementById('chapter-title').value;
        const chapterContent = document.getElementById('chapter-content').value;

        if (!chapterContent.trim()) {
            this.showSystemNotification('Chapter content cannot be empty', 'error');
            return;
        }

        try {
            const response = await fetch('/api/chapters', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    chapterNumber: chapterNumber,
                    title: chapterTitle,
                    content: chapterContent
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showSystemNotification('Chapter saved successfully!', 'success');
                this.closeEditor();
                await this.loadChapters();
                await this.loadGameState();
                this.updateUI();
            } else {
                this.showSystemNotification('Failed to save chapter', 'error');
            }
        } catch (error) {
            console.error('Error saving chapter:', error);
            this.showSystemNotification('Error saving chapter', 'error');
        }
    }

    previewChapter() {
        const content = document.getElementById('chapter-content').value;
        if (!content.trim()) {
            this.showSystemNotification('No content to preview', 'error');
            return;
        }

        // Simple preview - could be enhanced with markdown rendering
        const previewWindow = window.open('', '_blank');
        previewWindow.document.write(`
            <html>
                <head>
                    <title>Chapter Preview</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: #fff; }
                        pre { white-space: pre-wrap; }
                    </style>
                </head>
                <body>
                    <h1>Chapter Preview</h1>
                    <pre>${content}</pre>
                </body>
            </html>
        `);
    }
}

// Global functions for HTML onclick handlers
function showStory() { vikramSystem.showStory(); }
function showCharacters() { vikramSystem.showCharacters(); }
function showQuests() { vikramSystem.showQuests(); }
function showInventory() { vikramSystem.showInventory(); }
function showShop() { vikramSystem.showShop(); }
function openEditor() { vikramSystem.openEditor(); }
function closeEditor() { vikramSystem.closeEditor(); }
function saveChapter() { vikramSystem.saveChapter(); }
function previewChapter() { vikramSystem.previewChapter(); }
function hidePanel(panelId) { vikramSystem.hidePanel(panelId); }

// Initialize the system when page loads
let vikramSystem;
document.addEventListener('DOMContentLoaded', () => {
    vikramSystem = new VikramSystem();
});
