const express = require('express');
const cors = require('cors');
const fs = require('fs-extra');
const path = require('path');
const { marked } = require('marked');
const _ = require('lodash');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.static('public'));

// Data directories
const DATA_DIR = path.join(__dirname, 'data');
const CHAPTERS_DIR = path.join(__dirname, 'chapters');

// Ensure directories exist
fs.ensureDirSync(DATA_DIR);
fs.ensureDirSync(CHAPTERS_DIR);

// Game state management
class GameState {
    constructor() {
        this.characters = new Map();
        this.items = new Map();
        this.quests = new Map();
        this.locations = new Map();
        this.gameStats = {
            corruptionPoints: 0,
            systemPoints: 0,
            deviantExperience: 0,
            wealth: 21200000000, // 212 crores
            currentLocation: 'Malviya <PERSON> Flat',
            currentTime: '09:13 PM',
            mood: 'Numb, Confused',
            activeQuests: []
        };
        this.loadGameData();
    }

    loadGameData() {
        try {
            const gameDataPath = path.join(DATA_DIR, 'gamestate.json');
            if (fs.existsSync(gameDataPath)) {
                const data = fs.readJsonSync(gameDataPath);
                Object.assign(this, data);
                // Convert Maps back from objects
                this.characters = new Map(Object.entries(data.characters || {}));
                this.items = new Map(Object.entries(data.items || {}));
                this.quests = new Map(Object.entries(data.quests || {}));
                this.locations = new Map(Object.entries(data.locations || {}));
            }
        } catch (error) {
            console.log('No existing game data found, starting fresh');
        }
    }

    saveGameData() {
        const gameDataPath = path.join(DATA_DIR, 'gamestate.json');
        const dataToSave = {
            characters: Object.fromEntries(this.characters),
            items: Object.fromEntries(this.items),
            quests: Object.fromEntries(this.quests),
            locations: Object.fromEntries(this.locations),
            gameStats: this.gameStats
        };
        fs.writeJsonSync(gameDataPath, dataToSave, { spaces: 2 });
    }

    parseChapterContent(content) {
        // Extract system notifications
        const systemRegex = /`\[(.*?)\]`/g;
        const systemNotifications = [];
        let match;
        while ((match = systemRegex.exec(content)) !== null) {
            systemNotifications.push(match[1]);
        }

        // Extract character profiles
        const profileRegex = />\s*\*\*Designation:\*\*(.*?)(?=\n\n|\n>|\n`|\n\*\*|$)/gs;
        while ((match = profileRegex.exec(content)) !== null) {
            this.parseCharacterProfile(match[0]);
        }

        // Extract quest information
        const questRegex = />\s*\*\*Objective:\*\*(.*?)(?=\n\n|\n>|\n`|$)/gs;
        while ((match = questRegex.exec(content)) !== null) {
            this.parseQuestInfo(match[0]);
        }

        return {
            systemNotifications,
            parsedContent: content
        };
    }

    parseCharacterProfile(profileText) {
        const nameMatch = profileText.match(/\[(.*?)\]/);
        if (!nameMatch) return;

        const name = nameMatch[1];
        const character = {
            name,
            designation: this.extractField(profileText, 'Designation'),
            age: this.extractField(profileText, 'Age'),
            status: this.extractField(profileText, 'Status'),
            assetEvaluation: this.extractField(profileText, 'Asset Evaluation'),
            lustMeter: this.extractField(profileText, 'Lust Meter'),
            corruptionPotential: this.extractField(profileText, 'Corruption Potential'),
            lastUpdated: new Date().toISOString()
        };

        this.characters.set(name, character);
    }

    parseQuestInfo(questText) {
        const objectiveMatch = questText.match(/\*\*Objective:\*\*(.*?)(?=\n|\*\*)/);
        if (!objectiveMatch) return;

        const questId = Date.now().toString();
        const quest = {
            id: questId,
            objective: objectiveMatch[1].trim(),
            description: this.extractField(questText, 'Description'),
            task: this.extractField(questText, 'Task'),
            reward: this.extractField(questText, 'Reward'),
            status: 'active',
            createdAt: new Date().toISOString()
        };

        this.quests.set(questId, quest);
    }

    extractField(text, fieldName) {
        const regex = new RegExp(`\\*\\*${fieldName}:\\*\\*\\s*(.*?)(?=\\n\\*\\*|\\n>|$)`, 's');
        const match = text.match(regex);
        return match ? match[1].trim() : '';
    }
}

const gameState = new GameState();

// API Routes

// Get game state
app.get('/api/gamestate', (req, res) => {
    res.json({
        characters: Object.fromEntries(gameState.characters),
        items: Object.fromEntries(gameState.items),
        quests: Object.fromEntries(gameState.quests),
        locations: Object.fromEntries(gameState.locations),
        gameStats: gameState.gameStats
    });
});

// Get all chapters
app.get('/api/chapters', (req, res) => {
    try {
        const chapters = [];
        const files = fs.readdirSync(CHAPTERS_DIR).filter(f => f.endsWith('.md')).sort();
        
        files.forEach(file => {
            const content = fs.readFileSync(path.join(CHAPTERS_DIR, file), 'utf8');
            const chapterNumber = file.match(/chapter(\d+)/i)?.[1] || '0';
            chapters.push({
                id: parseInt(chapterNumber),
                filename: file,
                title: content.match(/###\s*\*\*(.+?)\*\*/)?.[1] || `Chapter ${chapterNumber}`,
                content: content,
                wordCount: content.split(/\s+/).length
            });
        });

        res.json(chapters);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Get specific chapter
app.get('/api/chapters/:id', (req, res) => {
    try {
        const chapterId = req.params.id;
        const filename = `chapter${chapterId}.md`;
        const filePath = path.join(CHAPTERS_DIR, filename);
        
        if (!fs.existsSync(filePath)) {
            return res.status(404).json({ error: 'Chapter not found' });
        }

        const content = fs.readFileSync(filePath, 'utf8');
        const parsed = gameState.parseChapterContent(content);
        
        res.json({
            id: parseInt(chapterId),
            filename,
            content: content,
            parsed: parsed,
            html: marked(content)
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Add new chapter
app.post('/api/chapters', (req, res) => {
    try {
        const { title, content, chapterNumber } = req.body;
        
        if (!content) {
            return res.status(400).json({ error: 'Content is required' });
        }

        const filename = `chapter${chapterNumber || Date.now()}.md`;
        const filePath = path.join(CHAPTERS_DIR, filename);
        
        fs.writeFileSync(filePath, content);
        
        // Parse and update game state
        gameState.parseChapterContent(content);
        gameState.saveGameData();
        
        res.json({ 
            success: true, 
            filename,
            message: 'Chapter added successfully'
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Update game stats
app.post('/api/gamestate/update', (req, res) => {
    try {
        const updates = req.body;
        Object.assign(gameState.gameStats, updates);
        gameState.saveGameData();
        
        res.json({ success: true, gameStats: gameState.gameStats });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Serve the main application
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start server
app.listen(PORT, () => {
    console.log(`🎮 Vikram's Immersive Novel running on http://localhost:${PORT}`);
    console.log(`📝 Chapter Editor: http://localhost:${PORT}/editor`);
    console.log(`🎯 Game Interface: http://localhost:${PORT}/game`);
});
