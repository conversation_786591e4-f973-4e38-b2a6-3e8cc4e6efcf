# <PERSON><PERSON><PERSON>'s Immersive Novel - Setup Script
Write-Host "🎮 Setting up <PERSON><PERSON><PERSON>'s Immersive Novel System..." -ForegroundColor Green

# Check if Docker is installed
try {
    docker --version | Out-Null
    Write-Host "✅ Docker is installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not installed. Please install Docker Desktop first." -ForegroundColor Red
    Write-Host "Download from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    exit 1
}

# Check if Docker Compose is available
try {
    docker-compose --version | Out-Null
    Write-Host "✅ Docker Compose is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose is not available. Please install Docker Compose." -ForegroundColor Red
    exit 1
}

# Create necessary directories
Write-Host "📁 Creating directories..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "data" | Out-Null
New-Item -ItemType Directory -Force -Path "chapters" | Out-Null
New-Item -ItemType Directory -Force -Path "public\js" | Out-Null
New-Item -ItemType Directory -Force -Path "public\styles" | Out-Null

Write-Host "✅ Directories created" -ForegroundColor Green

# Build and start the application
Write-Host "🔨 Building Docker containers..." -ForegroundColor Yellow
docker-compose build

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Docker build successful" -ForegroundColor Green
    
    Write-Host "🚀 Starting the application..." -ForegroundColor Yellow
    docker-compose up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Application started successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "🎯 Access your application at:" -ForegroundColor Cyan
        Write-Host "   Main Interface: http://localhost" -ForegroundColor White
        Write-Host "   Direct Access:  http://localhost:3000" -ForegroundColor White
        Write-Host ""
        Write-Host "📝 For Gemini to add chapters:" -ForegroundColor Cyan
        Write-Host "   1. Open http://localhost in browser" -ForegroundColor White
        Write-Host "   2. Click 'ADD CHAPTER' button" -ForegroundColor White
        Write-Host "   3. Write chapter content and save" -ForegroundColor White
        Write-Host ""
        Write-Host "🛑 To stop the application:" -ForegroundColor Cyan
        Write-Host "   docker-compose down" -ForegroundColor White
        Write-Host ""
        Write-Host "📊 To view logs:" -ForegroundColor Cyan
        Write-Host "   docker-compose logs -f" -ForegroundColor White
    } else {
        Write-Host "❌ Failed to start application" -ForegroundColor Red
        Write-Host "Check logs with: docker-compose logs" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Docker build failed" -ForegroundColor Red
    Write-Host "Check the error messages above" -ForegroundColor Yellow
}
