/* System Theme - Dark, Intimidating, Futuristic */
:root {
    --primary-bg: #0a0a0a;
    --secondary-bg: #1a1a1a;
    --accent-bg: #2a2a2a;
    --primary-text: #00ff41;
    --secondary-text: #ffffff;
    --danger-text: #ff0040;
    --warning-text: #ffaa00;
    --border-color: #333333;
    --glow-color: #00ff41;
    --corruption-color: #ff0040;
    --system-font: 'Orbitron', monospace;
    --content-font: '<PERSON><PERSON><PERSON>', sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, var(--primary-bg) 0%, #1a0a1a 100%);
    color: var(--secondary-text);
    font-family: var(--content-font);
    min-height: 100vh;
    overflow-x: hidden;
}

/* System Overlay */
#system-overlay {
    position: relative;
    min-height: 100vh;
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 255, 65, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 64, 0.1) 0%, transparent 50%);
}

/* Status Bar */
#status-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid var(--border-color);
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    font-family: var(--system-font);
    font-size: 14px;
    box-shadow: 0 2px 20px rgba(0, 255, 65, 0.2);
}

.status-section {
    display: flex;
    align-items: center;
    gap: 5px;
}

.status-label {
    color: var(--primary-text);
    font-weight: 700;
    text-shadow: 0 0 5px var(--glow-color);
}

#corruption-points {
    color: var(--corruption-color);
    font-weight: 700;
    text-shadow: 0 0 5px var(--corruption-color);
}

/* Main Menu */
#main-menu {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 80px 20px 20px;
}

.system-title {
    font-family: var(--system-font);
    font-size: 3.5rem;
    font-weight: 900;
    color: var(--primary-text);
    text-shadow: 0 0 20px var(--glow-color);
    margin-bottom: 50px;
    letter-spacing: 3px;
    animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
    from { text-shadow: 0 0 20px var(--glow-color); }
    to { text-shadow: 0 0 30px var(--glow-color), 0 0 40px var(--glow-color); }
}

.menu-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    max-width: 800px;
    width: 100%;
}

.menu-btn {
    background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--accent-bg) 100%);
    border: 2px solid var(--border-color);
    color: var(--secondary-text);
    padding: 20px 30px;
    font-size: 1.2rem;
    font-family: var(--content-font);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.menu-btn:hover {
    border-color: var(--primary-text);
    color: var(--primary-text);
    box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
    transform: translateY(-2px);
}

.menu-btn.editor-btn {
    border-color: var(--warning-text);
    color: var(--warning-text);
}

.menu-btn.editor-btn:hover {
    box-shadow: 0 0 20px rgba(255, 170, 0, 0.3);
}

/* Panels */
.panel {
    position: fixed;
    top: 60px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 1200px;
    height: calc(100vh - 80px);
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(15px);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    z-index: 999;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 50px rgba(0, 0, 0, 0.8);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 2px solid var(--border-color);
    background: linear-gradient(135deg, var(--accent-bg) 0%, var(--secondary-bg) 100%);
}

.panel-header h2 {
    font-family: var(--system-font);
    color: var(--primary-text);
    font-size: 1.5rem;
    text-shadow: 0 0 10px var(--glow-color);
}

.close-btn {
    background: none;
    border: none;
    color: var(--danger-text);
    font-size: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-btn:hover {
    color: #ffffff;
    text-shadow: 0 0 10px var(--danger-text);
}

.panel-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

/* Chapter List */
.chapter-item {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chapter-item:hover {
    border-color: var(--primary-text);
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.2);
}

.chapter-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-text);
    margin-bottom: 10px;
}

.chapter-meta {
    color: #888;
    font-size: 0.9rem;
}

/* Character Cards */
.character-card {
    background: var(--secondary-bg);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    position: relative;
}

.character-name {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--primary-text);
    margin-bottom: 15px;
}

.character-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-label {
    color: #ccc;
    font-weight: 500;
}

.stat-value {
    color: var(--secondary-text);
    font-weight: 600;
}

.lust-meter {
    color: var(--corruption-color) !important;
    text-shadow: 0 0 5px var(--corruption-color);
}

/* Hidden class */
.hidden {
    display: none !important;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--secondary-bg);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 2px solid var(--border-color);
    background: var(--accent-bg);
}

.modal-body {
    padding: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: var(--primary-text);
    font-weight: 600;
    margin-bottom: 8px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    background: var(--primary-bg);
    border: 2px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    color: var(--secondary-text);
    font-family: var(--content-font);
    font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-text);
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
}

.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-family: var(--content-font);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-text) 0%, #00cc33 100%);
    color: var(--primary-bg);
}

.btn-primary:hover {
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.5);
}

.btn-secondary {
    background: var(--accent-bg);
    color: var(--secondary-text);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    border-color: var(--primary-text);
    color: var(--primary-text);
}

/* Notifications */
#notifications-container {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1500;
}

.notification {
    background: rgba(26, 26, 26, 0.95);
    border: 2px solid var(--primary-text);
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 10px;
    color: var(--primary-text);
    font-family: var(--system-font);
    font-size: 0.9rem;
    box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .system-title {
        font-size: 2.5rem;
    }
    
    .menu-options {
        grid-template-columns: 1fr;
    }
    
    .panel {
        width: 95%;
        top: 70px;
        height: calc(100vh - 90px);
    }
    
    .panel-content {
        padding: 20px;
    }
}
