<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON>'s System - Main Interface</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div id="system-overlay">
        <!-- System Status Bar -->
        <div id="status-bar">
            <div class="status-section">
                <span class="status-label">CP:</span>
                <span id="corruption-points">0</span>
            </div>
            <div class="status-section">
                <span class="status-label">SP:</span>
                <span id="system-points">0</span>
            </div>
            <div class="status-section">
                <span class="status-label">DXP:</span>
                <span id="deviant-exp">0</span>
            </div>
            <div class="status-section">
                <span class="status-label">₹:</span>
                <span id="wealth">212Cr</span>
            </div>
            <div class="status-section">
                <span class="status-label">Location:</span>
                <span id="location">Malviya Nagar</span>
            </div>
            <div class="status-section">
                <span class="status-label">Time:</span>
                <span id="current-time">09:13 PM</span>
            </div>
        </div>

        <!-- Main Menu -->
        <div id="main-menu">
            <h1 class="system-title">VIKRAM'S SYSTEM</h1>
            <div class="menu-options">
                <button class="menu-btn" onclick="showStory()">📖 READ STORY</button>
                <button class="menu-btn" onclick="showCharacters()">👥 CHARACTERS</button>
                <button class="menu-btn" onclick="showQuests()">🎯 QUESTS</button>
                <button class="menu-btn" onclick="showInventory()">🎒 INVENTORY</button>
                <button class="menu-btn" onclick="showShop()">🛒 SHOP</button>
                <button class="menu-btn editor-btn" onclick="openEditor()">✏️ ADD CHAPTER</button>
            </div>
        </div>

        <!-- Story Reader -->
        <div id="story-panel" class="panel hidden">
            <div class="panel-header">
                <h2>📖 Story Chapters</h2>
                <button class="close-btn" onclick="hidePanel('story-panel')">&times;</button>
            </div>
            <div class="panel-content">
                <div id="chapter-list"></div>
                <div id="chapter-content" class="hidden"></div>
            </div>
        </div>

        <!-- Characters Panel -->
        <div id="characters-panel" class="panel hidden">
            <div class="panel-header">
                <h2>👥 Character Profiles</h2>
                <button class="close-btn" onclick="hidePanel('characters-panel')">&times;</button>
            </div>
            <div class="panel-content">
                <div id="character-list"></div>
            </div>
        </div>

        <!-- Quests Panel -->
        <div id="quests-panel" class="panel hidden">
            <div class="panel-header">
                <h2>🎯 Active Quests</h2>
                <button class="close-btn" onclick="hidePanel('quests-panel')">&times;</button>
            </div>
            <div class="panel-content">
                <div id="quest-list"></div>
            </div>
        </div>

        <!-- Inventory Panel -->
        <div id="inventory-panel" class="panel hidden">
            <div class="panel-header">
                <h2>🎒 Inventory</h2>
                <button class="close-btn" onclick="hidePanel('inventory-panel')">&times;</button>
            </div>
            <div class="panel-content">
                <div id="inventory-list"></div>
            </div>
        </div>

        <!-- Shop Panel -->
        <div id="shop-panel" class="panel hidden">
            <div class="panel-header">
                <h2>🛒 System Shop</h2>
                <button class="close-btn" onclick="hidePanel('shop-panel')">&times;</button>
            </div>
            <div class="panel-content">
                <div class="shop-categories">
                    <button class="shop-tab active" onclick="showShopCategory('consumables')">Consumables</button>
                    <button class="shop-tab" onclick="showShopCategory('espionage')">Espionage</button>
                    <button class="shop-tab" onclick="showShopCategory('skills')">Skills</button>
                </div>
                <div id="shop-items"></div>
            </div>
        </div>

        <!-- System Notifications -->
        <div id="notifications-container"></div>
    </div>

    <!-- Chapter Editor Modal -->
    <div id="editor-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>✏️ Add New Chapter</h2>
                <button class="close-btn" onclick="closeEditor()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="chapter-number">Chapter Number:</label>
                    <input type="number" id="chapter-number" placeholder="4">
                </div>
                <div class="form-group">
                    <label for="chapter-title">Chapter Title:</label>
                    <input type="text" id="chapter-title" placeholder="The Next Phase">
                </div>
                <div class="form-group">
                    <label for="chapter-content">Chapter Content:</label>
                    <textarea id="chapter-content" rows="20" placeholder="### **Chapter 4: The Next Phase**

**Location:** 
**Time:** 
**Mood:** 
**Active Quest(s):** 

Write your chapter content here..."></textarea>
                </div>
                <div class="form-actions">
                    <button class="btn-primary" onclick="saveChapter()">💾 Save Chapter</button>
                    <button class="btn-secondary" onclick="previewChapter()">👁️ Preview</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
