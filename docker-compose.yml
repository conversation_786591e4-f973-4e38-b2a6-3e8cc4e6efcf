version: '3.8'

services:
  vikram-game:
    build: .
    ports:
      - "3000:3000"
    volumes:
      - ./data:/app/data
      - ./chapters:/app/chapters
    environment:
      - NODE_ENV=development
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - vikram-game
    restart: unless-stopped
