{"name": "vikram-immersive-novel", "version": "1.0.0", "description": "Immersive gaming experience for <PERSON><PERSON><PERSON>'s story", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'Build complete'"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "marked": "^9.1.2", "socket.io": "^4.7.2", "fs-extra": "^11.1.1", "lodash": "^4.17.21", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["novel", "gaming", "immersive", "interactive"], "author": "<PERSON><PERSON>ram Game System", "license": "MIT"}