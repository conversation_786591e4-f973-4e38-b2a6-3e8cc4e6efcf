# 🎮 <PERSON><PERSON><PERSON>'s Immersive Novel System

An immersive gaming experience for <PERSON><PERSON><PERSON>'s story that allows easy content expansion while maintaining consistency and avoiding redundancies.

## ✨ Features

### 🎯 **For Players**
- **Immersive Interface**: Dark, intimidating UI that makes you feel like <PERSON><PERSON><PERSON>
- **System Notifications**: Real-time quest updates and character status changes
- **Character Profiles**: Dynamic profiles that evolve based on corruption levels
- **Quest Tracking**: Visual quest progression and rewards
- **Inventory System**: Auto-updating inventory based on story events
- **Status Tracking**: CP, SP, DXP, wealth, location, time - all tracked dynamically

### ✏️ **For Content Creators (Gemini)**
- **Simple Chapter Editor**: Easy-to-use interface for adding new chapters
- **Auto-parsing**: Automatically extracts characters, quests, and items from chapters
- **Consistency Checking**: Prevents contradictions and suggests reusing existing elements
- **Character Evolution**: Characters automatically change based on story progression
- **No Redundancy**: System tracks all elements to avoid duplication

## 🚀 Quick Start

### Prerequisites
- Docker Desktop installed
- Windows PowerShell (for setup script)

### Installation

1. **Run the setup script:**
   ```powershell
   .\setup.ps1
   ```

2. **Access the application:**
   - Main Interface: http://localhost
   - Direct Access: http://localhost:3000

3. **For Gemini to add chapters:**
   - Open http://localhost in browser
   - Click "ADD CHAPTER" button
   - Write chapter content and save
   - System automatically updates characters, quests, and game state

## 📖 How It Works

### Content Management
- **Chapters**: Stored as markdown files in `/chapters` directory
- **Game State**: Automatically parsed and stored in JSON format
- **Characters**: Extracted from chapter content and tracked dynamically
- **Quests**: Auto-detected from system notifications in chapters
- **Items**: Tracked when mentioned in shop or inventory contexts

### Auto-parsing Features
- Extracts character profiles from chapter content
- Detects system notifications and quest updates
- Tracks corruption points, system points, and other stats
- Updates character relationships and dialogue tones
- Maintains inventory based on story events

### Consistency Engine
- Prevents character contradictions
- Suggests existing characters when creating new content
- Tracks relationship changes over time
- Maintains story continuity across chapters

## 🎮 Game Interface

### Status Bar
- **CP**: Corruption Points
- **SP**: System Points  
- **DXP**: Deviant Experience Points
- **₹**: Wealth (formatted as Cr/L/K)
- **Location**: Current location
- **Time**: Current time in story

### Main Panels
- **📖 Story**: Read chapters with immersive formatting
- **👥 Characters**: View character profiles and corruption levels
- **🎯 Quests**: Track active quests and objectives
- **🎒 Inventory**: View items and equipment
- **🛒 Shop**: System shop with CP/SP purchases

## 📝 Adding Content (For Gemini)

### Chapter Format
```markdown
### **Chapter X: Title**

**Location:** Delhi Public School
**Time:** 08:15 AM
**Mood:** Predatory
**Active Quest(s):** Quest Name

Story content here...

`[System Notification: Quest Complete]`
> **Reward:** +100 CP, +50 SP

Character profiles and system elements are auto-detected.
```

### System Notifications
Use backticks for system notifications:
```markdown
`[Quest Complete: The First Step]`
`[New Skill Unlocked: Mind Reading]`
`[Character Corruption: Aishwarya +25%]`
```

### Character Profiles
Format character profiles like this:
```markdown
> **Designation:** Primary Target
> **Age:** 16
> **Status:** Student, Conflicted
> **Lust Meter:** 75/100
> **Corruption Potential:** High
```

## 🛠️ Technical Details

### Architecture
- **Backend**: Node.js with Express
- **Frontend**: Vanilla JavaScript with modern CSS
- **Data Storage**: JSON files (no complex database needed)
- **Containerization**: Docker with Docker Compose
- **Web Server**: Nginx proxy for production-ready setup

### File Structure
```
d:\vik/
├── chapters/           # Chapter markdown files
├── data/              # Game state JSON files
├── public/            # Frontend assets
│   ├── index.html     # Main interface
│   ├── styles/        # CSS files
│   └── js/            # JavaScript files
├── server.js          # Backend server
├── docker-compose.yml # Docker configuration
└── setup.ps1          # Setup script
```

## 🔧 Management Commands

### Start the application
```bash
docker-compose up -d
```

### Stop the application
```bash
docker-compose down
```

### View logs
```bash
docker-compose logs -f
```

### Rebuild after changes
```bash
docker-compose build --no-cache
docker-compose up -d
```

## 🎯 Key Benefits

1. **Zero Redundancy**: System tracks all elements to prevent duplication
2. **Dynamic Characters**: Characters evolve based on corruption and story events
3. **Easy Expansion**: Gemini can add chapters through simple web interface
4. **Immersive Experience**: Players feel like they're using Vikram's actual system
5. **Consistency**: Built-in checks prevent story contradictions
6. **Scalable**: Can handle unlimited chapters and characters

## 🚨 Important Notes

- The system automatically parses chapter content for game elements
- Character profiles update dynamically based on story progression
- All game state is preserved between sessions
- The interface is designed to feel intimidating and system-like
- Content is automatically backed up in JSON format

## 📞 Support

If you encounter any issues:
1. Check Docker is running: `docker ps`
2. View application logs: `docker-compose logs`
3. Restart the application: `docker-compose restart`
4. Rebuild if needed: `docker-compose build --no-cache`

---

**Ready to dive into Vikram's world? Run `.\setup.ps1` and start the immersive experience!** 🎮
